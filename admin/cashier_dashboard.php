<?php
// Start session before including any files that might try to modify session settings
session_start();

// Define secure access constant for config.php
define('SECURE_ACCESS', true);

// Include configuration
require_once '../config/config.php';

// Session timeout check using the function from security_helpers.php
if (session_expired()) {
    session_unset();
    session_destroy();
    header("Location: login.php");
    exit();
}

// Check if user is cashier
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'cashier') {
    header("Location: login.php");
    exit();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Cashier Dashboard - DBTI Online Registration">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.1/css/all.min.css" />
    <link rel="icon" href="img/logo.webp" type="image/png">
    <title>Cashier Dashboard | DBTI Online</title>

    <style>
        * {
            padding: 0;
            margin: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            color: #333;
            background-color: #f5f5f5;
            scroll-behavior: smooth;
        }

        /* Navbar */
        nav {
            background-color: #ffbf00;
            padding: 20px 5%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
            width: 100%;
        }

        nav .logo {
            height: 50px;
            width: auto;
            margin-right: 15px;
        }

        nav .heading {
            font-size: 1.75rem;
            font-weight: bold;
            color: #fff;
        }

        nav ul {
            list-style: none;
            display: flex;
            gap: 20px;
        }

        nav ul li a {
            font-size: 1rem;
            color: #fff;
            text-transform: uppercase;
            padding: 12px 20px;
            background-color: #cc9900;
            border-radius: 6px;
            font-weight: 700;
            transition: all 0.3s ease;
            display: inline-block;
            text-decoration: none;
        }

        nav ul li a:hover {
            background-color: #996600;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        /* Sidebar */
        .sidebar {
            width: 250px;
            height: calc(100vh - 220px);
            background: white;
            position: fixed;
            left: 0;
            top: 80px;
            padding: 20px;
            box-shadow: 4px 0 10px rgba(0, 0, 0, 0.1);
            z-index: 100;
            margin-bottom: 60px;
        }

        .sidebar a {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            color: #333;
            text-decoration: none;
            font-size: 1.2rem;
            font-weight: 700;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .sidebar a i {
            margin-right: 10px;
            font-size: 1.4rem;
        }

        .sidebar a:hover {
            background: #f0f0f0;
            transform: translateX(5px);
        }

        /* Main Content */
        .main-content {
            margin-left: 250px;
            padding: 20px;
        }

        .main-content h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.2rem;
            color: #333;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }

        .info-box {
            text-align: center;
            max-width: 800px;
            margin: 0 auto;
            padding: 30px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        /* Stats Section */
        .stats {
            display: flex;
            gap: 60px;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
            padding: 20px;
        }

        .stat-box {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            width: 300px;
            text-align: center;
            transition: transform 0.3s ease;
        }

        .stat-box:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        .stat-box h3 {
            color: #333;
            font-size: 1.4rem;
            margin-bottom: 15px;
        }

        .stat-box p {
            font-size: 2rem;
            font-weight: bold;
            color: #4CAF50;
        }

        /* Footer */
        footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 15px 0;
            position: fixed;
            bottom: 0;
            width: 100%;
        }

        footer a {
            color: #ffbf00;
            text-decoration: none;
        }

        /* Mobile Responsive */
        .hamburger {
            display: none;
            flex-direction: column;
            cursor: pointer;
        }

        .hamburger div {
            width: 25px;
            height: 3px;
            background-color: white;
            margin: 4px 0;
            transition: 0.4s;
        }

        @media only screen and (max-width: 768px) {
            nav {
                flex-wrap: wrap;
            }

            nav .logo {
                height: 40px;
            }

            nav ul {
                display: none;
                flex-direction: column;
                width: 100%;
                text-align: center;
                background-color: #ffbf00;
                padding: 15px 0;
            }

            nav ul.active {
                display: flex;
            }

            nav ul li a {
                width: 200px;
                margin: 8px auto;
                text-align: center;
                display: block;
            }

            .hamburger {
                display: flex;
            }

            nav .heading {
                font-size: 1.2rem;
            }

            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
                top: 0;
            }

            .main-content {
                margin-left: 0;
                padding: 15px;
            }

            .stats {
                gap: 20px;
                padding: 10px;
            }

            .stat-box {
                width: 100%;
                max-width: 320px;
            }
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 80%;
            height: 80%;
            background-color: rgba(255, 255, 255, 1);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            overflow: hidden;
        }

        .modal-content {
            width: 100%;
            height: 100%;
            border: none;
        }

        .close {
            position: absolute;
            right: 10px;
            top: 10px;
            font-size: 24px;
            cursor: pointer;
        }

        @media screen and (max-width: 768px) {
            .modal {
                width: 95%;
                height: 90%;
            }
        }
    </style>
</head>
<body>
    <nav>
        <img src="img/logo.webp" alt="DBTI Logo" class="logo">
        <div class="heading">DBTI Online Registration</div>
        <div class="hamburger" onclick="toggleNavbar()">
            <div></div>
            <div></div>
            <div></div>
        </div>
        <ul id="navbar">
            <li><a href="index.php">Home</a></li>
            <li><a href="about.php">About</a></li>
            <li><a href="cashier_dashboard.php">Dashboard</a></li>
            <li><a href="logout.php">Logout <i class="fa-solid fa-user"></i></a></li>
        </ul>
    </nav>

    <div class="sidebar">
        <a href="#" onclick="showStats()">
            <i class="fa-solid fa-chart-simple"></i>
            <span>Stats</span>
        </a>
        <a href="stripe/sync_payments.php">
            <i class="fa-solid fa-sync"></i>
            <span>Sync Payments</span>
        </a>
        <a href="stripe/process_pending.php">
            <i class="fa-solid fa-tasks"></i>
            <span>Process Pending</span>
        </a>
        <a href="stripe/webhook_test_ui.php">
            <i class="fa-solid fa-bolt"></i>
            <span>Test Webhooks</span>
        </a>
        <a href="stripe/check_stripe.php">
            <i class="fa-solid fa-cogs"></i>
            <span>Check Stripe Setup</span>
        </a>
        <a href="stripe/check_database.php">
            <i class="fa-solid fa-database"></i>
            <span>Check Database</span>
        </a>
    </div>

    <div class="main-content">
        <h1>Welcome Cashier!</h1>
        <div class="stats">
            <?php
            // Include database connection
            require 'db_conn.php';

            // Query to count total students who paid
            $sql = "SELECT COUNT(DISTINCT student_id) as total_paid_students FROM payments WHERE payment_method = 'credit_card'";
            $result = $conn->query($sql);
            $row = $result->fetch_assoc();
            $totalPaidStudents = $row['total_paid_students'];

            // Query to sum total amount paid
            $sql = "SELECT SUM(amount) as total_amount FROM payments WHERE payment_method = 'credit_card'";
            $result = $conn->query($sql);
            $row = $result->fetch_assoc();
            $totalAmount = $row['total_amount'];

            // Query to count total transactions
            $sql = "SELECT COUNT(*) as total_transactions FROM payments WHERE payment_method = 'credit_card'";
            $result = $conn->query($sql);
            $row = $result->fetch_assoc();
            $totalTransactions = $row['total_transactions'];

            // Query to count payments in last 30 days
            $sql = "SELECT COUNT(*) as recent_payments FROM payments
                    WHERE payment_method = 'credit_card'
                    AND payment_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
            $result = $conn->query($sql);
            $row = $result->fetch_assoc();
            $recentPayments = $row['recent_payments'];
            ?>

            <!-- Display the stats -->
            <div class="stat-box">
                <h3>Total Students Paid Online</h3>
                <p id="totalStudentsPaid"><?php echo $totalPaidStudents; ?></p>
            </div>
            <div class="stat-box">
                <h3>Total Amount Paid Online</h3>
                <p>K<span id="totalAmountPaid"><?php echo number_format($totalAmount, 2); ?></span></p>
            </div>
            <div class="stat-box">
                <h3>Total Transactions Made</h3>
                <p id="totalTransactions"><?php echo $totalTransactions; ?></p>
            </div>
            <div class="stat-box">
                <h3>Payments Last 30 Days</h3>
                <p id="recentPayments"><?php echo $recentPayments; ?></p>
            </div>
        </div>
    </div>
    <footer>
        <div class="footer">
            <span>
                Copyright © 2024 Don Bosco Technological Institute. All Rights Reserved.
                <a href="https://www.dbti.ac.pg/" target="_blank">DBTI Website</a>
            </span>
        </div>
    </footer>

    <!-- Add this modal structure after your main content -->
    <div id="statsModal" class="modal">
        <span class="close" onclick="closeStats()">&times;</span>
        <iframe id="statsIframe" class="modal-content" src=""></iframe>
    </div>

    <script>
        function toggleNavbar() {
            const navbar = document.getElementById('navbar');
            navbar.classList.toggle('active');
        }

        function showStats() {
            document.getElementById("statsIframe").src = "stripe/transection_page.php";
            document.getElementById("statsModal").style.display = "block";
        }

        function closeStats() {
            document.getElementById("statsModal").style.display = "none";
            document.getElementById("statsIframe").src = ""; // Clear the iframe source
        }

        // Close the modal when clicking outside of the modal content
        window.onclick = function(event) {
            var modal = document.getElementById("statsModal");
            if (event.target == modal) {
                closeStats();
            }
        };
    </script>
</body>
</html>
